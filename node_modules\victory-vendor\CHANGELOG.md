# victory-vendor

## 36.9.2

## 36.9.1

## 36.9.0

## 36.8.6

## 36.8.5

### Patch Changes

- Replace instances of lodash.assign with Object.assign ([#2757](https://github.com/FormidableLabs/victory/pull/2757))

## 36.8.4

## 36.8.3

## 36.8.2

## 36.8.1

## 36.8.0

## 36.7.0

## 36.6.12

## 36.6.11

## 36.6.10

### Patch Changes

- Setup NPM Provenance ([#2590](https://github.com/FormidableLabs/victory/pull/2590))

## 36.6.9

### Patch Changes

- Setup NPM Provenance ([#2587](https://github.com/FormidableLabs/victory/pull/2587))

## 36.6.8

## 36.6.7

## 36.6.6

## 36.6.5

### Patch Changes

- Export types directly from d3-\* (fixes [#2439](https://github.com/FormidableLabs/victory/issues/2439)) ([#2440](https://github.com/FormidableLabs/victory/pull/2440))

## 36.6.4

### Patch Changes

- Allow data accessors to accept any data types (fixes [#2360](https://github.com/FormidableLabs/victory/issues/2360)) ([#2436](https://github.com/FormidableLabs/victory/pull/2436))

## 36.6.3

### Patch Changes

- Do not generate \*.js.map sourcemaps (fixes [#2346](https://github.com/FormidableLabs/victory/issues/2346)) ([#2432](https://github.com/FormidableLabs/victory/pull/2432))

## 36.6.2

## 36.6.1

## 36.6.0

### Patch Changes

- Update source code with minor lint-based improvements (see [#2236](https://github.com/FormidableLabs/victory/issues/2236)). ([#2403](https://github.com/FormidableLabs/victory/pull/2403))

## 36.5.3 and earlier

Change history for version 36.5.3 and earlier can be found in our root [CHANGELOG.md](https://github.com/FormidableLabs/victory/blob/main/CHANGELOG.md).
