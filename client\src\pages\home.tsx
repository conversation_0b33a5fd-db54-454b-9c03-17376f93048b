import { useState } from "react";
import { <PERSON> } from "wouter";
import { Play } from "lucide-react";
import { Head<PERSON> } from "@/components/Header";
import { CategoryNavigation } from "@/components/CategoryNavigation";
import { VideoGrid } from "@/components/VideoGrid";
import { AdBanner } from "@/components/AdBanner";
import { Footer } from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export default function Home() {
  const [selectedCategory, setSelectedCategory] = useState<number | undefined>(undefined);

  return (
    <div className="min-h-screen bg-primary text-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-primary to-secondary py-12 md:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-4xl md:text-6xl font-bold leading-tight">
                Premium Video
                <span className="text-accent"> Streaming</span>
              </h2>
              <p className="text-xl text-neutral max-w-lg">
                Discover thousands of high-quality videos across multiple categories. 
                Stream instantly with our advanced player technology.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg"
                  className="bg-accent hover:bg-accent/90 text-white px-8 py-4"
                >
                  Start Watching
                </Button>
                <Button 
                  size="lg"
                  variant="outline"
                  className="border-neutral hover:border-white text-neutral hover:text-white px-8 py-4"
                >
                  Browse Categories
                </Button>
              </div>
            </div>
            
            
          </div>
        </div>
      </section>

      {/* Top Banner Ad - Responsive */}
      <section className="py-4 bg-secondary/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Mobile Banner */}
          <div className="block md:hidden">
            <AdBanner width={320} height={50} network="adsterra" />
          </div>
          {/* Desktop Banner */}
          <div className="hidden md:block">
            <AdBanner width={728} height={90} network="direct" />
          </div>
        </div>
      </section>

      {/* Category Navigation */}
      <CategoryNavigation 
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
      />

      {/* Video Grid */}
      <VideoGrid categoryId={selectedCategory} />

      {/* Bottom Banner Ad - Responsive */}
      <section className="py-8 bg-secondary/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Mobile Banner */}
          <div className="block md:hidden">
            <AdBanner width={320} height={50} network="direct" />
          </div>
          {/* Desktop Banner */}
          <div className="hidden md:block">
            <AdBanner width={728} height={90} network="adsterra" />
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
