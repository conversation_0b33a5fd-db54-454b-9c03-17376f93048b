import { useState } from "react";
import { useLocation } from "wouter";
import { useVideos } from "@/hooks/useVideos";
import { VideoCard } from "./VideoCard";
import { AdBanner } from "./AdBanner";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Grid, List, ChevronDown } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import type { Video } from "@shared/schema";

interface VideoGridProps {
  categoryId?: number;
}

export function VideoGrid({ categoryId }: VideoGridProps) {
  const [, setLocation] = useLocation();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('latest');
  const [offset, setOffset] = useState(0);
  const limit = 20;

  const { data: videos, isLoading, error } = useVideos(categoryId, limit, offset);

  const handleVideoClick = (video: Video) => {
    // Navigate to video detail page instead of opening player
    setLocation(`/video-detail/${video.id}`);
  };

  const handleLoadMore = () => {
    setOffset(prev => prev + limit);
  };

  if (isLoading && offset === 0) {
    return (
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <Skeleton className="h-8 w-48" />
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-20" />
            </div>
          </div>
          
          <div className="mb-8">
            <Skeleton className="h-24 w-full" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-secondary rounded-xl overflow-hidden animate-pulse">
                <div className="aspect-video bg-neutral/20" />
                <div className="p-4">
                  <div className="h-4 bg-neutral/20 rounded mb-2" />
                  <div className="h-3 bg-neutral/20 rounded w-1/2 mb-3" />
                  <div className="flex justify-between">
                    <div className="h-3 bg-neutral/20 rounded w-1/3" />
                    <div className="h-3 bg-neutral/20 rounded w-4" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-red-500">
            <h2 className="text-2xl font-bold mb-4">Error Loading Videos</h2>
            <p>Failed to load videos. Please try again later.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <>
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold">Latest Videos</h2>
            <div className="flex items-center space-x-4">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48 bg-secondary text-white border-neutral/20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="latest">Sort by Latest</SelectItem>
                  <SelectItem value="popular">Sort by Popular</SelectItem>
                  <SelectItem value="duration">Sort by Duration</SelectItem>
                </SelectContent>
              </Select>
              
              <div className="flex space-x-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'secondary'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className={viewMode === 'grid' ? 'bg-accent' : ''}
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'secondary'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={viewMode === 'list' ? 'bg-accent' : ''}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Responsive Ad Banner */}
          <div className="mb-8">
            <div className="hidden md:block">
              <AdBanner width={728} height={90} network="adsterra" />
            </div>
            <div className="md:hidden">
              <AdBanner width={320} height={50} network="direct" />
            </div>
          </div>

          {/* Video Grid */}
          {videos && videos.length > 0 ? (
            <>
              <div className={`grid gap-6 ${
                viewMode === 'grid' 
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {videos.map((video) => (
                  <VideoCard
                    key={video.id}
                    video={video}
                    onClick={handleVideoClick}
                  />
                ))}
              </div>

              {/* Load More Button */}
              <div className="text-center mt-12">
                <Button
                  onClick={handleLoadMore}
                  disabled={isLoading}
                  className="bg-accent hover:bg-accent/90 text-white px-8 py-4 rounded-lg font-semibold transition-colors duration-200 inline-flex items-center space-x-2"
                >
                  <span>{isLoading ? 'Loading...' : 'Load More Videos'}</span>
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold mb-4">No Videos Found</h3>
              <p className="text-neutral">
                {categoryId 
                  ? "No videos found in this category. Try selecting a different category."
                  : "No videos are currently available. Please check back later."
                }
              </p>
            </div>
          )}
        </div>
      </section>
    </>
  );
}
