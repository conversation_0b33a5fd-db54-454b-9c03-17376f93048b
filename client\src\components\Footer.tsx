import { But<PERSON> } from "@/components/ui/button";

export function Footer() {
  const handleTelegramContact = () => {
    window.open('https://t.me/buckyop', '_blank');
  };

  return (
    <footer className="bg-secondary border-t border-neutral/20 py-8 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-accent">VideoAdStream</h3>
            <p className="text-neutral">
              Premium video streaming platform with advanced features and high-quality content.
            </p>
          </div>

          {/* Contact */}
          <div>
            <h4 className="font-semibold mb-4 text-white">Contact</h4>
            <p className="text-neutral mb-4">
              For content removal or other inquiries, contact us on Telegram:
            </p>
            <Button
              onClick={handleTelegramContact}
              className="bg-accent hover:bg-accent/90 text-white"
            >
              📱 Contact @buckyop
            </Button>
          </div>
        </div>
      </div>
    </footer>
  );
}