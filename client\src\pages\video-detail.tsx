import { useParams } from "wouter";
import { useVideo } from "../hooks/useVideos";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AdBanner } from "@/components/AdBanner";
import { formatViews, formatTimeAgo, formatDuration } from "@/lib/videoUtils";
import { useEffect } from "react";

export default function VideoDetail() {
  const { id } = useParams<{ id: string }>();
  const videoId = parseInt(id || "0");
  const { data: video, isLoading, error } = useVideo(videoId);

  useEffect(() => {
    // Load Adsterra popunder script
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = '//trianglesickdemocrat.com/c7/c8/51/c7c85199ffa6852b75585dede9a1d108.js';
    document.head.appendChild(script);

    return () => {
      // Cleanup script when component unmounts
      const scripts = document.querySelectorAll('script[src*="trianglesickdemocrat.com"]');
      scripts.forEach(s => s.remove());
    };
  }, []);

  const handleWatchVideo = () => {
    if (video) {
      // Open video in new tab to trigger popunder
      window.open(video.url, '_blank');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded w-3/4 mb-6"></div>
            <div className="h-96 bg-slate-200 dark:bg-slate-700 rounded mb-6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="container mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-4">
            Video Not Found
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            The video you're looking for doesn't exist or has been removed.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        {/* Top Ad Banner */}
        <div className="mb-8">
          <AdBanner 
            width={728} 
            height={90} 
            className="hidden md:block" 
            network="adsterra" 
          />
          <AdBanner 
            width={320} 
            height={50} 
            className="block md:hidden" 
            network="direct" 
          />
        </div>

        {/* Video Details Card */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl text-slate-800 dark:text-slate-200">
              {video.title}
            </CardTitle>
            <CardDescription className="flex items-center gap-4 text-sm text-slate-600 dark:text-slate-400">
              <span>{formatViews(video.views || 0)} views</span>
              <span>•</span>
              <span>{formatTimeAgo(video.createdAt ? new Date(video.createdAt) : new Date())}</span>
              {video.duration && (
                <>
                  <span>•</span>
                  <span>{formatDuration(video.duration)}</span>
                </>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Video Thumbnail */}
            <div className="relative mb-6">
              <img 
                src={video.thumbnailUrl || '/api/placeholder/640/360'} 
                alt={video.title}
                className="w-full h-64 md:h-96 object-cover rounded-lg"
              />
              <div className="absolute inset-0 bg-black bg-opacity-20 rounded-lg flex items-center justify-center">
                <Button
                  onClick={handleWatchVideo}
                  size="lg"
                  className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 text-lg font-semibold shadow-lg"
                >
                  ▶ Watch Video
                </Button>
              </div>
            </div>

            {/* Video Description */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200">
                About this video
              </h3>
              <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                Enjoy this amazing video content. Click the 'Watch Video' button above to start watching.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Side Ad Banner */}
        <div className="mb-8">
          <AdBanner 
            width={728} 
            height={90} 
            className="hidden md:block" 
            network="direct" 
          />
          <AdBanner 
            width={320} 
            height={50} 
            className="block md:hidden" 
            network="adsterra" 
          />
        </div>

        {/* Bottom Ad Banner */}
        <div className="mb-8">
          <AdBanner 
            width={728} 
            height={90} 
            className="hidden md:block" 
            network="adsterra" 
          />
          <AdBanner 
            width={320} 
            height={50} 
            className="block md:hidden" 
            network="direct" 
          />
        </div>
      </div>
    </div>
  );
}