import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import type { Video, Category } from "@shared/schema";

export function useVideos(categoryId?: number, limit = 20, offset = 0) {
  return useQuery({
    queryKey: ["/api/videos", categoryId, limit, offset],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (categoryId) params.append("categoryId", categoryId.toString());
      params.append("limit", limit.toString());
      params.append("offset", offset.toString());
      
      const response = await fetch(`/api/videos?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch videos");
      }
      return response.json() as Promise<Video[]>;
    },
  });
}

export function useVideo(id: number) {
  return useQuery({
    queryKey: ["/api/videos", id],
    queryFn: async () => {
      const response = await fetch(`/api/videos/${id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch video");
      }
      return response.json() as Promise<Video>;
    },
  });
}

export function useCategories() {
  return useQuery({
    queryKey: ["/api/categories"],
    queryFn: async () => {
      const response = await fetch("/api/categories");
      if (!response.ok) {
        throw new Error("Failed to fetch categories");
      }
      return response.json() as Promise<Category[]>;
    },
  });
}

export function useUpdateVideoViews() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (videoId: number) => {
      const response = await apiRequest("POST", `/api/videos/${videoId}/view`);
      return response.json();
    },
    onSuccess: (data, videoId) => {
      queryClient.setQueryData(["/api/videos", videoId], data);
      queryClient.invalidateQueries({ queryKey: ["/api/videos"] });
    },
  });
}

export function useSearchVideos(query: string) {
  return useQuery({
    queryKey: ["/api/videos/search", query],
    queryFn: async () => {
      if (!query.trim()) return [];
      
      const response = await fetch(`/api/videos/search?q=${encodeURIComponent(query)}`);
      if (!response.ok) {
        throw new Error("Failed to search videos");
      }
      return response.json() as Promise<Video[]>;
    },
    enabled: !!query.trim(),
  });
}
