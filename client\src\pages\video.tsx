import { useParams } from "wouter";
import { useVideo } from "@/hooks/useVideos";
import { Header } from "@/components/Header";
import { VideoPlayer } from "@/components/VideoPlayer";
import { Footer } from "@/components/Footer";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";

export default function VideoPage() {
  const { id } = useParams<{ id: string }>();
  const videoId = parseInt(id || "0");
  const { data: video, isLoading, error } = useVideo(videoId);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-primary text-white">
        <Header />
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Skeleton className="aspect-video w-full mb-6" />
          <Skeleton className="h-8 w-3/4 mb-4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="min-h-screen bg-primary text-white">
        <Header />
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="bg-secondary">
            <CardContent className="p-8 text-center">
              <h1 className="text-2xl font-bold mb-4">Video Not Found</h1>
              <p className="text-neutral">
                The video you're looking for doesn't exist or has been removed.
              </p>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-primary text-white">
      <Header />
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <VideoPlayer video={video} onClose={() => window.history.back()} />
      </div>
      <Footer />
    </div>
  );
}
