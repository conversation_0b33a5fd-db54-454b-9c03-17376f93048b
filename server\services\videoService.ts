import { storage } from "../storage";
import { type InsertVideo } from "@shared/schema";

export class VideoService {
  static async parseEnvironmentVideos() {
    const videos: InsertVideo[] = [];
    const categories = await storage.getCategories();
    
    // Parse environment variables for video content
    // Format: category_[name]_video[n]_URL and category_[name]_video[n]_title
    const envKeys = Object.keys(process.env);
    
    for (const key of envKeys) {
      const urlMatch = key.match(/^category_(\w+)_video(\d+)_URL$/i);
      if (urlMatch) {
        const categoryName = urlMatch[1];
        const videoNumber = urlMatch[2];
        const titleKey = `category_${categoryName}_video${videoNumber}_title`;
        
        const url = process.env[key];
        const title = process.env[titleKey];
        
        if (url && title) {
          // Find category by name
          const category = categories.find(cat => 
            cat.name.toLowerCase() === categoryName.toLowerCase()
          );
          
          if (category) {
            const thumbnailUrl = await this.generateThumbnail(url);
            const duration = await this.extractDuration(url);
            
            videos.push({
              title,
              url,
              thumbnailUrl,
              categoryId: category.id,
              duration,
            });
          }
        }
      }
    }
    
    return videos;
  }

  static async generateThumbnail(videoUrl: string): Promise<string | undefined> {
    try {
      // YouTube URL handling
      const youtubeMatch = videoUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
      if (youtubeMatch) {
        return `https://img.youtube.com/vi/${youtubeMatch[1]}/maxresdefault.jpg`;
      }
      
      // Vimeo URL handling
      const vimeoMatch = videoUrl.match(/vimeo\.com\/(\d+)/);
      if (vimeoMatch) {
        return `https://vumbnail.com/${vimeoMatch[1]}.jpg`;
      }

      // Dailymotion URL handling
      const dailymotionMatch = videoUrl.match(/dailymotion\.com\/video\/([^_]+)/);
      if (dailymotionMatch) {
        return `https://www.dailymotion.com/thumbnail/video/${dailymotionMatch[1]}`;
      }

      // Twitch URL handling
      const twitchMatch = videoUrl.match(/twitch\.tv\/videos\/(\d+)/);
      if (twitchMatch) {
        return `https://static-cdn.jtvnw.net/cf_vods/d2nvs31859zcd8/twitchvod/${twitchMatch[1]}//thumb/thumb0-320x240.jpg`;
      }

      // Generic video file extensions
      const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.wmv', '.flv', '.mkv'];
      const hasVideoExtension = videoExtensions.some(ext => videoUrl.toLowerCase().includes(ext));
      
      if (hasVideoExtension) {
        // For direct video files, we'll try to extract the first frame
        // This is a placeholder - in production you'd want to use a service like ffmpeg
        return this.extractVideoThumbnail(videoUrl);
      }

      // Try to extract thumbnail from other video hosting platforms
      return this.extractGenericThumbnail(videoUrl);
    } catch (error) {
      console.error('Error generating thumbnail:', error);
      return undefined;
    }
  }

  static async extractVideoThumbnail(videoUrl: string): Promise<string | undefined> {
    try {
      // For direct video files, we could use a service to extract thumbnails
      // This is a placeholder implementation
      return undefined;
    } catch (error) {
      console.error('Error extracting video thumbnail:', error);
      return undefined;
    }
  }

  static async extractGenericThumbnail(videoUrl: string): Promise<string | undefined> {
    try {
      // Try to fetch Open Graph meta tags for thumbnail
      const response = await fetch(videoUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      if (!response.ok) {
        return undefined;
      }
      
      const html = await response.text();
      
      // Extract Open Graph image
      const ogImageMatch = html.match(/<meta\s+property="og:image"\s+content="([^"]+)"/i);
      if (ogImageMatch) {
        return ogImageMatch[1];
      }
      
      // Extract Twitter card image
      const twitterImageMatch = html.match(/<meta\s+name="twitter:image"\s+content="([^"]+)"/i);
      if (twitterImageMatch) {
        return twitterImageMatch[1];
      }
      
      // Extract first image from page
      const imgMatch = html.match(/<img[^>]+src="([^"]+)"/i);
      if (imgMatch) {
        const imgSrc = imgMatch[1];
        // Make sure it's a full URL
        if (imgSrc.startsWith('http')) {
          return imgSrc;
        } else if (imgSrc.startsWith('//')) {
          return `https:${imgSrc}`;
        } else if (imgSrc.startsWith('/')) {
          const baseUrl = new URL(videoUrl).origin;
          return `${baseUrl}${imgSrc}`;
        }
      }
      
      return undefined;
    } catch (error) {
      console.error('Error extracting generic thumbnail:', error);
      return undefined;
    }
  }

  static async extractDuration(videoUrl: string): Promise<number | undefined> {
    try {
      // This would require actual video analysis
      // For now, return undefined as duration extraction requires more complex implementation
      return undefined;
    } catch (error) {
      console.error('Error extracting duration:', error);
      return undefined;
    }
  }

  static async initializeVideosFromEnvironment() {
    const videos = await this.parseEnvironmentVideos();
    
    for (const video of videos) {
      try {
        await storage.createVideo(video);
      } catch (error) {
        console.error('Error creating video:', error);
      }
    }
    
    return videos;
  }

  static isValidVideoUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const validDomains = [
        'youtube.com',
        'youtu.be',
        'vimeo.com',
        'dailymotion.com',
        'twitch.tv',
      ];
      
      return validDomains.some(domain => urlObj.hostname.includes(domain));
    } catch {
      return false;
    }
  }
}
