@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(210, 23%, 8%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(217, 24%, 12%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --accent: hsl(0, 65%, 67%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  --neutral: hsl(215, 10%, 57%);
  --success: hsl(142, 71%, 45%);
  --error: hsl(0, 84%, 60%);
}

.dark {
  --background: hsl(210, 23%, 8%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(217, 24%, 12%);
  --muted-foreground: hsl(215, 10%, 57%);
  --popover: hsl(217, 24%, 12%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(217, 24%, 12%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(217, 24%, 12%);
  --input: hsl(217, 24%, 12%);
  --primary: hsl(210, 23%, 8%);
  --primary-foreground: hsl(0, 0%, 98%);
  --secondary: hsl(217, 24%, 12%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(0, 65%, 67%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  --neutral: hsl(215, 10%, 57%);
  --success: hsl(142, 71%, 45%);
  --error: hsl(0, 84%, 60%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}

/* Video Player Styles */
.video-player {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-player video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  padding: 1rem;
  transition: opacity 0.3s ease;
}

.video-controls.hidden {
  opacity: 0;
  pointer-events: none;
}

/* Ad Styles */
.ad-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(30, 41, 59, 0.3);
  border: 2px dashed rgba(139, 148, 158, 0.3);
  border-radius: 0.5rem;
  color: hsl(215, 10%, 57%);
}

.ad-container.loaded {
  background: transparent;
  border: none;
}

/* Loading States */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: 1fr;
  }
  
  .video-grid.list-view {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .video-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .video-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1281px) {
  .video-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Styles */
.focus-visible:focus {
  outline: 2px solid hsl(0, 65%, 67%);
  outline-offset: 2px;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(217, 24%, 12%);
}

::-webkit-scrollbar-thumb {
  background: hsl(215, 10%, 57%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(0, 65%, 67%);
}
