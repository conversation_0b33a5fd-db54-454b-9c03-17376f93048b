import { Play, Eye, Clock, Bookmark } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { formatViews, formatTimeAgo, formatDuration, getThumbnailUrl } from "@/lib/videoUtils";
import type { Video } from "@shared/schema";

interface VideoCardProps {
  video: Video;
  onClick: (video: Video) => void;
}

export function VideoCard({ video, onClick }: VideoCardProps) {
  const thumbnailUrl = video.thumbnailUrl || getThumbnailUrl(video.url) || 
    `https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=450`;

  return (
    <Card 
      className="bg-secondary rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:transform hover:scale-105 group cursor-pointer"
      onClick={() => onClick(video)}
    >
      <div className="relative aspect-video">
        <img
          src={thumbnailUrl}
          alt={video.title}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = `https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=450`;
          }}
        />
        
        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-all duration-300" />
        
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center">
            <Play className="text-white text-xl ml-1" fill="currentColor" />
          </div>
        </div>
        
        {video.duration && (
          <div className="absolute bottom-3 right-3 bg-black/80 text-white px-2 py-1 rounded text-sm">
            {formatDuration(video.duration)}
          </div>
        )}
      </div>
      
      <CardContent className="p-4">
        <h3 className="font-semibold text-lg mb-2 line-clamp-2 text-white">
          {video.title}
        </h3>
        
        <p className="text-neutral text-sm mb-3">
          {video.createdAt && formatTimeAgo(new Date(video.createdAt))}
        </p>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-neutral">
            <span className="flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              {formatViews(video.views || 0)} views
            </span>
            
            {video.duration && (
              <span className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                {formatDuration(video.duration)}
              </span>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-neutral hover:text-accent transition-colors duration-200"
            onClick={(e) => {
              e.stopPropagation();
              // Handle bookmark functionality
            }}
          >
            <Bookmark className="w-4 h-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
