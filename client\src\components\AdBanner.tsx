import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

interface AdBannerProps {
  width: number;
  height: number;
  className?: string;
  network?: 'adsterra' | 'direct';
}

export function AdBanner({ width, height, className, network = 'adsterra' }: AdBannerProps) {
  const [adHtml, setAdHtml] = useState<string>("");

  useEffect(() => {
    const fetchAdContent = async () => {
      try {
        const response = await fetch(`/api/ads/banner?width=${width}&height=${height}&network=${network}`);
        const data = await response.json();
        setAdHtml(data.html);
      } catch (error) {
        console.error('Failed to load ad content:', error);
      }
    };

    // Load Adsterra and direct link ads on first banner load
    const loadAllAds = async () => {
      try {
        const response = await fetch('/api/ads/scripts');
        const data = await response.json();
        
        // Load Adsterra popunder
        if (data.adsterra) {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = data.adsterra;
          const script = tempDiv.querySelector('script');
          if (script) {
            const newScript = document.createElement('script');
            newScript.src = script.src;
            document.head.appendChild(newScript);
          }
        }
        
        // Load direct link ads
        if (data.directlinks) {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = data.directlinks;
          const script = tempDiv.querySelector('script');
          if (script) {
            const newScript = document.createElement('script');
            newScript.textContent = script.textContent;
            document.head.appendChild(newScript);
          }
        }
      } catch (error) {
        console.error('Failed to load ads:', error);
      }
    };

    fetchAdContent();
    
    // Only load ads once per session
    if (!sessionStorage.getItem('ads_loaded')) {
      loadAllAds();
      sessionStorage.setItem('ads_loaded', 'true');
    }
  }, [width, height, network]);

  useEffect(() => {
    if (adHtml) {
      // Execute any scripts in the ad HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = adHtml;
      const scripts = tempDiv.querySelectorAll('script');
      
      scripts.forEach(script => {
        const newScript = document.createElement('script');
        if (script.src) {
          newScript.src = script.src;
        } else {
          newScript.textContent = script.textContent;
        }
        
        // Copy all attributes
        Array.from(script.attributes).forEach(attr => {
          newScript.setAttribute(attr.name, attr.value);
        });
        
        document.head.appendChild(newScript);
      });
    }
  }, [adHtml]);

  return (
    <div className={cn("ad-banner", className)}>
      {adHtml ? (
        <div dangerouslySetInnerHTML={{ __html: adHtml }} />
      ) : (
        <div 
          className="bg-secondary/30 border-2 border-dashed border-neutral/30 rounded-lg flex items-center justify-center"
          style={{ width: `${width}px`, height: `${height}px`, margin: '0 auto' }}
        >
          <div className="text-center text-neutral">
            <div className="text-2xl mb-2">📢</div>
            <p className="font-medium">Advertisement Space</p>
            <p className="text-sm">{width}x{height} {network} Banner</p>
          </div>
        </div>
      )}
    </div>
  );
}
