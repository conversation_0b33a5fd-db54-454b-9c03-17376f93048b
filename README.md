# VideoAdStream - Video Sharing Platform

A modern video sharing platform with automatic thumbnail extraction and ad network integration. Built with React, TypeScript, and Express.js.

## Features

- **Environment-based Video Management**: Add videos through environment variables
- **Automatic Thumbnail Extraction**: Supports YouTube, Vimeo, Dailymotion, Twitch, and more
- **Ad Network Integration**: ExoClick and PopAds support with banner and popunder ads
- **Responsive Design**: Works on desktop and mobile devices
- **Category System**: Organize videos by categories
- **Search Functionality**: Find videos quickly
- **Video Player**: Embedded video player with view tracking

## Deployment on Render

### 1. Environment Variables

Set up the following environment variables in your Render dashboard:

#### Database (Optional - uses in-memory storage by default)
```
DATABASE_URL=postgresql://username:password@hostname:port/database
```

#### Ad Networks
```
EXOCLICK_ZONE_ID=your_exoclick_zone_id
EXOCLICK_BANNER_ZONE_ID=your_exoclick_banner_zone_id
POPADS_SITE_ID=your_popads_site_id
POPADS_BANNER_ZONE_ID=your_popads_banner_zone_id
```

#### Video Content
Use this format to add videos:
```
category_[category_name]_video[number]_URL=video_url
category_[category_name]_video[number]_title=video_title
```

**Available Categories**: entertainment, education, technology, sports, music, news

**Example**:
```
category_entertainment_video1_URL=https://www.youtube.com/watch?v=dQw4w9WgXcQ
category_entertainment_video1_title=Never Gonna Give You Up - Rick Astley

category_education_video1_URL=https://vimeo.com/123456789
category_education_video1_title=How to Learn Programming

category_technology_video1_URL=https://www.dailymotion.com/video/x7xyz123
category_technology_video1_title=Latest Tech Reviews
```

### 2. Deploy to Render

1. **Create a new Web Service** on Render
2. **Connect your GitHub repository**
3. **Configure build settings**:
   - Build Command: `npm install`
   - Start Command: `npm run dev`
   - Node Version: 18+
4. **Add environment variables** from the examples above
5. **Deploy**

### 3. Supported Video Platforms

The platform automatically extracts thumbnails from:

- **YouTube**: `youtube.com/watch?v=` or `youtu.be/`
- **Vimeo**: `vimeo.com/[video_id]`
- **Dailymotion**: `dailymotion.com/video/[video_id]`
- **Twitch**: `twitch.tv/videos/[video_id]`
- **Direct Video Files**: `.mp4`, `.webm`, `.ogg`, etc.
- **Generic Platforms**: Uses Open Graph meta tags

### 4. Ad Network Setup

#### ExoClick
1. Sign up at ExoClick.com
2. Create ad zones
3. Add zone IDs to environment variables

#### PopAds
1. Sign up at PopAds.net
2. Get your site ID
3. Add to environment variables

## Local Development

1. **Clone the repository**
```bash
git clone <your-repo-url>
cd videoadstream
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start development server**
```bash
npm run dev
```

5. **Open browser**
```
http://localhost:5000
```

## Project Structure

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # React hooks
│   │   └── lib/            # Utilities
├── server/                 # Express backend
│   ├── services/           # Business logic
│   │   ├── adService.ts    # Ad network integration
│   │   └── videoService.ts # Video processing
│   ├── routes.ts           # API routes
│   └── storage.ts          # Data storage
├── shared/                 # Shared types and schemas
└── .env.example            # Environment configuration example
```

## API Endpoints

- `GET /api/videos` - Get all videos (with category filter)
- `GET /api/videos/:id` - Get single video
- `POST /api/videos/:id/view` - Update video views
- `GET /api/categories` - Get all categories
- `GET /api/ads/banner` - Get banner ad HTML
- `GET /api/ads/popunder` - Get popunder ad HTML

## Adding New Categories

To add a new category, update the default categories in `server/storage.ts`:

```typescript
const defaultCategories = [
  { name: "Entertainment", slug: "entertainment", description: "Fun content" },
  { name: "Your New Category", slug: "yourcategory", description: "Description" },
  // ... other categories
];
```

Then use the category name in your environment variables:
```
category_yourcategory_video1_URL=https://example.com/video
category_yourcategory_video1_title=Sample Video Title
```

## Troubleshooting

### Videos not showing
1. Check environment variable format
2. Verify video URLs are accessible
3. Ensure category names match exactly

### Thumbnails not loading
1. Video platform may not support thumbnail extraction
2. Check if video URL is publicly accessible
3. Fallback placeholder will be used

### Ads not displaying
1. Verify ad network credentials
2. Check zone/site IDs are correct
3. Ensure ad network accounts are approved

## License

This project is licensed under the MIT License.