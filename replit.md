# VideoAdStream - Premium Video Streaming Platform

## Overview

VideoAdStream is a full-stack web application that provides a premium video streaming platform with advanced features and high-quality content. The application supports video playback, categorization, view tracking, and ad integration for monetization.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side routing
- **State Management**: TanStack Query (React Query) for server state management
- **UI Framework**: Radix UI components with shadcn/ui design system
- **Styling**: Tailwind CSS with CSS custom properties for theming
- **Build Tool**: Vite for development and production builds

### Backend Architecture
- **Runtime**: Node.js with Express.js server
- **Language**: TypeScript with ESM modules
- **Database**: PostgreSQL with Drizzle ORM
- **Database Provider**: Neon Database (serverless)
- **Storage**: In-memory storage implementation with interface for database abstraction

### Database Schema Design
- **Videos Table**: Stores video metadata (title, URL, thumbnail, category, duration, views, active status)
- **Categories Table**: Organizes videos into categories with slugs for SEO
- **Users Table**: Basic user authentication structure (prepared for future features)
- **Migrations**: Managed through Drizzle Kit

## Key Components

### Video Management
- **Video Service**: Handles video initialization from environment variables, thumbnail generation, and duration extraction
- **Video Player**: Custom React component with playback controls, view tracking, and embed URL handling
- **Video Grid**: Responsive grid layout with pagination, filtering, and sorting capabilities
- **Video Cards**: Interactive video previews with hover effects and metadata display

### Ad Integration
- **Ad Service**: Generates ad scripts for ExoClick and PopAds networks
- **Ad Banner Component**: Displays banner advertisements with fallback placeholders
- **Ad Networks**: Supports multiple ad networks (ExoClick, PopAds) with configurable zones

### User Interface
- **Responsive Design**: Mobile-first approach with desktop enhancements
- **Dark/Light Theme**: CSS custom properties for theme switching
- **Component Library**: Comprehensive UI components from Radix UI
- **Typography**: Inter font family for consistent typography

## Data Flow

1. **Video Initialization**: Environment variables are parsed to create video entries
2. **Content Delivery**: Videos are served through the Express API with category filtering
3. **Client Interaction**: React components fetch data using TanStack Query
4. **View Tracking**: Video views are updated when users interact with the player
5. **Ad Serving**: Ad components fetch and display advertisements from configured networks

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL database connectivity
- **drizzle-orm**: Type-safe database queries and migrations
- **@tanstack/react-query**: Server state management and caching
- **@radix-ui/react-***: Accessible UI component primitives
- **wouter**: Lightweight React routing
- **zod**: Runtime type validation and schema definition

### Development Dependencies
- **vite**: Build tool and development server
- **tsx**: TypeScript execution for server development
- **esbuild**: Fast JavaScript bundler for production builds
- **tailwindcss**: Utility-first CSS framework

### Ad Networks
- **ExoClick**: Primary ad network for banner and pop ads
- **PopAds**: Secondary ad network for additional revenue streams

## Deployment Strategy

### Development Environment
- **Server**: Node.js with hot reloading via tsx
- **Client**: Vite development server with HMR
- **Database**: Neon Database with connection pooling

### Production Build
- **Client**: Static assets built with Vite and served from Express
- **Server**: Bundled with esbuild for optimized performance
- **Database**: Production Neon Database with environment-based configuration

### Environment Configuration
- **DATABASE_URL**: PostgreSQL connection string
- **Ad Network IDs**: ExoClick and PopAds configuration
- **Video Content**: Environment variables for video URLs and metadata

## Recent Changes

- **July 05, 2025: FINAL UPDATE - Complete ExoClick Removal & Direct Links Integration**
  - Completely removed ExoClick due to free hosting domain restrictions
  - Implemented Adsterra as primary popunder ad network (working perfectly)
  - Added 3 direct link ad networks: otieu.com + 2 trianglesickdemocrat.com links
  - Direct links trigger on 30% of user clicks for optimal user experience
  - Cleaned footer: removed social media, help sections, newsletter - only Telegram @buckyop contact
  - Created comprehensive deployment guide (DEPLOYMENT_GUIDE.md) with instructions for adding more ad networks
  - Both Adsterra and direct links confirmed working and triggering correctly

- July 04, 2025: Enhanced video sharing platform with improved thumbnail extraction
- Added support for multiple video platforms (YouTube, Vimeo, Dailymotion, Twitch)
- Implemented generic thumbnail extraction using Open Graph meta tags
- Enhanced ad network integration with proper zone ID handling
- Added popunder ad support for PopAds
- Created deployment-ready configuration for Render
- Added comprehensive README and environment configuration examples
- Fixed TypeScript type issues in storage layer

## Deployment Configuration

### Environment Variables Required:
- **NO AD NETWORK ENVIRONMENT VARIABLES NEEDED** - All ad networks work without configuration
- **Adsterra**: Hardcoded script `//trianglesickdemocrat.com/c7/c8/51/c7c85199ffa6852b75585dede9a1d108.js`
- **Direct Links**: Hardcoded in code
  - `https://otieu.com/4/9533347`
  - `https://trianglesickdemocrat.com/bmxmkkkh?key=dd338f660d87a15f0c0bd17c06f70a05`
  - `https://trianglesickdemocrat.com/cm6hc4db?key=5b794f555f9d3d619cbfcf33caa8516e`
- **Video Content**: Environment variable format
  - `category_[name]_video[n]_URL` - Video URL
  - `category_[name]_video[n]_title` - Video title
- **Contact**: Only Telegram @buckyop (no other contact methods)

### Supported Video Platforms:
- YouTube (automatic thumbnail extraction)
- Vimeo (automatic thumbnail extraction)
- Dailymotion (automatic thumbnail extraction)
- Twitch (automatic thumbnail extraction)
- Generic platforms (Open Graph meta tag extraction)
- Direct video files (.mp4, .webm, etc.)

## User Preferences

Preferred communication style: Simple, everyday language.