import { AlertTriangle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  onRetry?: () => void;
}

export function ErrorModal({ isOpen, onClose, title = "Error", message, onRetry }: ErrorModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md bg-secondary border-neutral/20">
        <DialogHeader className="text-center">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4 mx-auto">
            <AlertTriangle className="text-red-500 w-8 h-8" />
          </div>
          <DialogTitle className="text-xl font-semibold text-white mb-2">
            {title}
          </DialogTitle>
        </DialogHeader>
        
        <div className="text-center">
          <p className="text-neutral mb-6">{message}</p>
          
          <div className="flex space-x-3">
            {onRetry && (
              <Button 
                onClick={onRetry}
                className="bg-accent hover:bg-accent/90 text-white flex-1"
              >
                Retry
              </Button>
            )}
            <Button 
              onClick={onClose}
              variant="secondary"
              className="bg-secondary hover:bg-secondary/80 text-white flex-1"
            >
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
