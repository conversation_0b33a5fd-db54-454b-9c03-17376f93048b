import { useCategories } from "@/hooks/useVideos";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

interface CategoryNavigationProps {
  selectedCategory?: number;
  onCategoryChange: (categoryId?: number) => void;
}

export function CategoryNavigation({ selectedCategory, onCategoryChange }: CategoryNavigationProps) {
  const { data: categories, isLoading, error } = useCategories();

  if (isLoading) {
    return (
      <section className="py-8 bg-secondary/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-6">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-6 w-20" />
          </div>
          <div className="flex flex-wrap gap-3">
            {Array.from({ length: 7 }).map((_, index) => (
              <Skeleton key={index} className="h-12 w-24" />
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-8 bg-secondary/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-red-500">
            Failed to load categories
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-8 bg-secondary/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold">Browse Categories</h2>
          <Button
            variant="ghost"
            className="text-accent hover:text-accent/80 font-medium"
          >
            View All
          </Button>
        </div>
        
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={() => onCategoryChange(undefined)}
            className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
              !selectedCategory
                ? "bg-accent text-white hover:bg-accent/90"
                : "bg-secondary hover:bg-secondary/80 text-white"
            }`}
          >
            All Videos
          </Button>
          
          {categories?.map((category) => (
            <Button
              key={category.id}
              onClick={() => onCategoryChange(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                selectedCategory === category.id
                  ? "bg-accent text-white hover:bg-accent/90"
                  : "bg-secondary hover:bg-secondary/80 text-white"
              }`}
            >
              {category.name}
            </Button>
          ))}
        </div>
      </div>
    </section>
  );
}
