import { useState, useEffect } from "react";
import { X, Play, Pause, Volume2, Settings, Maximize, ThumbsUp, Share, Bookmark } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";
import { useUpdateVideoViews } from "@/hooks/useVideos";
import { getEmbedUrl, formatDuration, formatViews, formatTimeAgo } from "@/lib/videoUtils";
import type { Video } from "@shared/schema";

interface VideoPlayerProps {
  video: Video;
  onClose: () => void;
}

export function VideoPlayer({ video, onClose }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(video.duration || 0);
  const [volume, setVolume] = useState(100);
  const [showControls, setShowControls] = useState(true);
  
  const updateViewsMutation = useUpdateVideoViews();
  const embedUrl = getEmbedUrl(video.url);

  useEffect(() => {
    // Update video views when player opens
    updateViewsMutation.mutate(video.id);
  }, [video.id]);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (isPlaying && showControls) {
      timeout = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }
    return () => clearTimeout(timeout);
  }, [isPlaying, showControls]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (value: number[]) => {
    setCurrentTime(value[0]);
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0]);
  };

  const handleMouseMove = () => {
    setShowControls(true);
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl w-full max-h-[90vh] bg-secondary border-neutral/20 p-0">
        <DialogHeader className="p-4 border-b border-neutral/20">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold text-white">
              {video.title}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-neutral hover:text-white"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </DialogHeader>

        <div className="p-6">
          <div 
            className="aspect-video bg-black rounded-xl relative mb-4 overflow-hidden"
            onMouseMove={handleMouseMove}
          >
            {embedUrl ? (
              <iframe
                src={embedUrl}
                className="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-20 h-20 bg-accent rounded-full flex items-center justify-center mb-4 mx-auto">
                    <Play className="text-white text-2xl ml-1" fill="currentColor" />
                  </div>
                  <p className="text-white mb-2">Video Player</p>
                  <p className="text-neutral text-sm">
                    Unable to embed this video. 
                    <a 
                      href={video.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-accent hover:underline ml-1"
                    >
                      Watch on original site
                    </a>
                  </p>
                </div>
              </div>
            )}

            {/* Video Controls Overlay */}
            {!embedUrl && (
              <div 
                className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 transition-opacity duration-300 ${
                  showControls ? 'opacity-100' : 'opacity-0'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handlePlayPause}
                      className="text-white hover:text-accent"
                    >
                      {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                    </Button>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-white hover:text-accent"
                      >
                        <Volume2 className="w-4 h-4" />
                      </Button>
                      <Slider
                        value={[volume]}
                        onValueChange={handleVolumeChange}
                        max={100}
                        step={1}
                        className="w-20"
                      />
                    </div>
                    
                    <span className="text-white text-sm">
                      {formatDuration(currentTime)} / {formatDuration(duration)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-accent"
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-accent"
                    >
                      <Maximize className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <Slider
                  value={[currentTime]}
                  onValueChange={handleSeek}
                  max={duration}
                  step={1}
                  className="w-full"
                />
              </div>
            )}
          </div>

          {/* Video Info */}
          <div className="space-y-4">
            <div>
              <h4 className="text-lg font-semibold mb-2 text-white">
                {video.title}
              </h4>
              <p className="text-neutral text-sm">
                {video.createdAt && formatTimeAgo(new Date(video.createdAt))} • {formatViews(video.views || 0)} views
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button className="bg-accent hover:bg-accent/90 text-white">
                <ThumbsUp className="w-4 h-4 mr-2" />
                Like
              </Button>
              
              <Button variant="secondary" className="bg-secondary hover:bg-secondary/80 text-white">
                <Share className="w-4 h-4 mr-2" />
                Share
              </Button>
              
              <Button variant="secondary" className="bg-secondary hover:bg-secondary/80 text-white">
                <Bookmark className="w-4 h-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
