export class AdService {
  static generateAdsterraScript(): string {
    return `
      <!-- Adsterra Popunder -->
      <script type='text/javascript' src='//trianglesickdemocrat.com/c7/c8/51/c7c85199ffa6852b75585dede9a1d108.js'></script>
    `;
  }

  static generateDirectLinkAds(): string {
    return `
      <!-- Direct Link Ads -->
      <script type='text/javascript'>
        document.addEventListener('click', function(e) {
          const directLinks = [
            'https://otieu.com/4/9533347',
            'https://trianglesickdemocrat.com/bmxmkkkh?key=dd338f660d87a15f0c0bd17c06f70a05',
            'https://trianglesickdemocrat.com/cm6hc4db?key=5b794f555f9d3d619cbfcf33caa8516e'
          ];
          const randomLink = directLinks[Math.floor(Math.random() * directLinks.length)];
          if (Math.random() < 0.3) {
            window.open(randomLink, '_blank');
          }
        });
      </script>
    `;
  }

  static generateBannerAd(width: number, height: number, network: 'adsterra' | 'direct' = 'adsterra'): string {
    if (network === 'adsterra') {
      return `
        <div class="ad-container" style="width: ${width}px; height: ${height}px; margin: 0 auto; display: flex; align-items: center; justify-content: center; background: rgba(30, 41, 59, 0.3); border: 2px dashed rgba(139, 148, 158, 0.3); border-radius: 0.5rem; color: #8b94a2;">
          <div class="text-center">
            <div style="font-size: 24px; margin-bottom: 8px;">📢</div>
            <p style="font-weight: 600; margin: 0;">Advertisement Space</p>
            <p style="font-size: 14px; margin: 0; margin-top: 4px;">${width}x${height} Adsterra Banner</p>
          </div>
          <!-- Adsterra Banner Script -->
          <script type="text/javascript">
            (function() {
              var adsterra_banner = document.createElement('script');
              adsterra_banner.type = 'text/javascript';
              adsterra_banner.src = '//trianglesickdemocrat.com/c7/c8/51/c7c85199ffa6852b75585dede9a1d108.js';
              document.head.appendChild(adsterra_banner);
            })();
          </script>
        </div>
      `;
    } else {
      // ✅ Adsterra iframe banner (728x90 or similar)
      return `
        <div class="ad-container" style="width: ${width}px; height: ${height}px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
          <script type="text/javascript">
            atOptions = {
              'key': '474e41fa4ac52f033d280bfc5a772795',
              'format': 'iframe',
              'height': ${90},
              'width': ${728},
              'params': {}
            };
          </script>
          <script type="text/javascript" src="//trianglesickdemocrat.com/474e41fa4ac52f033d280bfc5a772795/invoke.js"></script>
        </div>
      `;
    }
  } // ✅ This was missing!

  static generatePopunderAd(): string {
    return `
      <!-- Adsterra Popunder -->
      <script type='text/javascript' src='//trianglesickdemocrat.com/c7/c8/51/c7c85199ffa6852b75585dede9a1d108.js'></script>
      
      <!-- Direct Link Ads -->
      ${this.generateDirectLinkAds()}
    `;
  }

  static generateAllAds(): string {
    return `
      <!-- All Ad Scripts Loading -->
      ${this.generateAdsterraScript()}
      ${this.generateDirectLinkAds()}
    `;
  }
}
