import { users, videos, categories, type User, type InsertUser, type Video, type InsertVideo, type Category, type InsertCategory } from "@shared/schema";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Video methods
  getVideos(categoryId?: number, limit?: number, offset?: number): Promise<Video[]>;
  getVideo(id: number): Promise<Video | undefined>;
  createVideo(video: InsertVideo): Promise<Video>;
  updateVideoViews(id: number): Promise<Video | undefined>;
  searchVideos(query: string): Promise<Video[]>;
  
  // Category methods
  getCategories(): Promise<Category[]>;
  getCategory(id: number): Promise<Category | undefined>;
  getCategoryBySlug(slug: string): Promise<Category | undefined>;
  createCategory(category: InsertCategory): Promise<Category>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private videos: Map<number, Video>;
  private categories: Map<number, Category>;
  private currentUserId: number;
  private currentVideoId: number;
  private currentCategoryId: number;

  constructor() {
    this.users = new Map();
    this.videos = new Map();
    this.categories = new Map();
    this.currentUserId = 1;
    this.currentVideoId = 1;
    this.currentCategoryId = 1;
    
    // Initialize with default categories
    this.initializeDefaultData();
  }

  private async initializeDefaultData() {
    const defaultCategories = [
      { name: "Leaks", slug: "leaks", description: "All leaks of Social Media" },
      { name: "Jav", slug: "jav", description: "Japanese" },
      { name: "Western", slug: "western", description: "Videos of West" },
      { name: "Desi", slug: "desi", description: "Desi Videos" },
      { name: "Famous P Star", slug: "star", description: "Famous P0rn Stars" },
      { name: "News & Tutorails", slug: "news", description: "Latest news and updates" },
    ];

    for (const category of defaultCategories) {
      await this.createCategory(category);
    }

    // No sample videos - will be populated from environment variables only
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Video methods
  async getVideos(categoryId?: number, limit = 20, offset = 0): Promise<Video[]> {
    let allVideos = Array.from(this.videos.values()).filter(video => video.isActive);
    
    if (categoryId) {
      allVideos = allVideos.filter(video => video.categoryId === categoryId);
    }
    
    // Sort by creation date (newest first)
    allVideos.sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime());
    
    return allVideos.slice(offset, offset + limit);
  }

  async getVideo(id: number): Promise<Video | undefined> {
    return this.videos.get(id);
  }

  async createVideo(insertVideo: InsertVideo): Promise<Video> {
    const id = this.currentVideoId++;
    const video: Video = {
      id,
      title: insertVideo.title,
      url: insertVideo.url,
      thumbnailUrl: insertVideo.thumbnailUrl || null,
      categoryId: insertVideo.categoryId || null,
      duration: insertVideo.duration || null,
      views: 1000,
      isActive: true,
      createdAt: new Date(),
    };
    this.videos.set(id, video);
    return video;
  }

  async updateVideoViews(id: number): Promise<Video | undefined> {
    const video = this.videos.get(id);
    if (video) {
      const updatedVideo = { ...video, views: (video.views || 0) + 1 };
      this.videos.set(id, updatedVideo);
      return updatedVideo;
    }
    return undefined;
  }

  async searchVideos(query: string): Promise<Video[]> {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.videos.values()).filter(video =>
      video.isActive && video.title.toLowerCase().includes(lowerQuery)
    );
  }

  // Category methods
  async getCategories(): Promise<Category[]> {
    return Array.from(this.categories.values());
  }

  async getCategory(id: number): Promise<Category | undefined> {
    return this.categories.get(id);
  }

  async getCategoryBySlug(slug: string): Promise<Category | undefined> {
    return Array.from(this.categories.values()).find(category => category.slug === slug);
  }

  async createCategory(insertCategory: InsertCategory): Promise<Category> {
    const id = this.currentCategoryId++;
    const category: Category = {
      id,
      name: insertCategory.name,
      slug: insertCategory.slug,
      description: insertCategory.description || null,
      createdAt: new Date(),
    };
    this.categories.set(id, category);
    return category;
  }
}

export const storage = new MemStorage();
